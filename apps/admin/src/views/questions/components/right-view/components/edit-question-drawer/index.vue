<script setup lang="ts">
import type { TransformToVoQuestionData } from '@sa/utils'
import CKEditor from '@sa/components/common/ck-editor/index.vue'
import type { FormInst } from 'naive-ui'

defineOptions({
  name: 'EditQuestionDrawer',
})

// 定义props
const props = defineProps<{
  question: TransformToVoQuestionData | null
}>()

// 定义emits
const emit = defineEmits<{
  save: [question: TransformToVoQuestionData]
  cancel: []
}>()

// 抽屉显示状态
const visible = defineModel('visible', {
  type: Boolean,
  default: false,
})

// 监听抽屉关闭
function handleClose() {
  visible.value = false
  emit('cancel')
}

// 保存题目
function handleSave() {
  if (props.question) {
    emit('save', props.question)
    visible.value = false
  }
}
const formRef = ref<FormInst | null>(null)

const dynamicForm = reactive({
  name: '',
  hobbies: [{ hobby: '' }],
})
</script>

<template>
  <NDrawer v-model:show="visible" :width="800" placement="right">
    <NDrawerContent :title="`编辑${question!.title}`" closable :native-scrollbar="false" @close="handleClose">
      <NForm ref="formRef" :model="dynamicForm" label-placement="left">
        <NFormItem label="题干" :rules="[{ required: true, message: '请输入姓名' }]" class="w-100%">
          <CKEditor />
        </NFormItem>
      </NForm>

      <!-- <div class="test w-500px">
        <CKEditor v-for="item in 10" :key="item" class="mb-20px" />
      </div> -->
      <!-- 抽屉底部操作按钮 -->
      <template #footer>
        <div class="flex justify-end gap-3">
          <NButton @click="handleClose">
            取消
          </NButton>
          <NButton type="primary" @click="handleSave">
            保存
          </NButton>
        </div>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
/* 自定义样式 */
</style>
